#!/usr/bin/env python3
"""
PRISMA批量下载器使用示例
演示如何使用类似GEE的方式批量下载PRISMA数据
"""

import pandas as pd
from datetime import datetime, timedelta
from prisma_downloader import PRISMADownloader, DownloadRequest
from config import PRISMA_USERNAME, PRISMA_PASSWORD

def example_single_download():
    """单个区域下载示例"""
    print("=== 单个区域下载示例 ===")
    
    # 创建下载请求 - 北京地区
    request = DownloadRequest(
        start_date="2023-06-01",
        end_date="2023-06-30",
        min_lat=39.5,
        max_lat=40.5,
        min_lon=116.0,
        max_lon=117.0,
        cloud_cover=15,
        product_level="L1"
    )
    
    # 执行下载
    downloader = PRISMADownloader(PRISMA_USERNAME, PRISMA_PASSWORD)
    
    try:
        results = downloader.batch_download([request])
        print(f"下载完成: {results}")
        
    finally:
        downloader.close()

def example_multiple_regions():
    """多个区域批量下载示例"""
    print("=== 多个区域批量下载示例 ===")
    
    # 定义多个感兴趣区域
    regions = [
        {
            "name": "北京",
            "bounds": (39.5, 40.5, 116.0, 117.0),
            "dates": ("2023-05-01", "2023-05-31")
        },
        {
            "name": "上海", 
            "bounds": (31.0, 31.5, 121.0, 121.8),
            "dates": ("2023-05-01", "2023-05-31")
        },
        {
            "name": "广州",
            "bounds": (23.0, 23.5, 113.0, 113.8),
            "dates": ("2023-05-01", "2023-05-31")
        }
    ]
    
    # 创建下载请求列表
    requests = []
    for region in regions:
        min_lat, max_lat, min_lon, max_lon = region["bounds"]
        start_date, end_date = region["dates"]
        
        request = DownloadRequest(
            start_date=start_date,
            end_date=end_date,
            min_lat=min_lat,
            max_lat=max_lat,
            min_lon=min_lon,
            max_lon=max_lon,
            cloud_cover=20,
            product_level="L1"
        )
        requests.append(request)
    
    # 执行批量下载
    downloader = PRISMADownloader(PRISMA_USERNAME, PRISMA_PASSWORD)
    
    try:
        results = downloader.batch_download(requests)
        
        # 打印结果
        for i, (region, result) in enumerate(zip(regions, results.values())):
            print(f"{region['name']}: 下载了 {len(result)} 个文件")
            
    finally:
        downloader.close()

def example_time_series():
    """时间序列下载示例"""
    print("=== 时间序列下载示例 ===")
    
    # 定义研究区域
    study_area = {
        "min_lat": 40.0,
        "max_lat": 40.5,
        "min_lon": 116.2,
        "max_lon": 116.8
    }
    
    # 生成月度时间序列请求
    requests = []
    start_date = datetime(2023, 1, 1)
    
    for month in range(12):
        current_date = start_date + timedelta(days=30 * month)
        end_date = current_date + timedelta(days=29)
        
        request = DownloadRequest(
            start_date=current_date.strftime("%Y-%m-%d"),
            end_date=end_date.strftime("%Y-%m-%d"),
            min_lat=study_area["min_lat"],
            max_lat=study_area["max_lat"],
            min_lon=study_area["min_lon"],
            max_lon=study_area["max_lon"],
            cloud_cover=10,
            product_level="L2B"
        )
        requests.append(request)
    
    # 执行下载
    downloader = PRISMADownloader(PRISMA_USERNAME, PRISMA_PASSWORD)
    
    try:
        results = downloader.batch_download(requests)
        print(f"时间序列下载完成，共处理 {len(requests)} 个月份")
        
    finally:
        downloader.close()

def example_csv_batch_download():
    """从CSV文件批量下载示例"""
    print("=== 从CSV文件批量下载示例 ===")
    
    # 创建示例CSV文件
    data = {
        'start_date': ['2023-04-01', '2023-04-01', '2023-04-01'],
        'end_date': ['2023-04-30', '2023-04-30', '2023-04-30'],
        'min_lat': [39.0, 31.0, 23.0],
        'max_lat': [41.0, 32.0, 24.0],
        'min_lon': [116.0, 121.0, 113.0],
        'max_lon': [118.0, 122.0, 114.0],
        'cloud_cover': [15, 20, 25],
        'product_level': ['L1', 'L1', 'L2B'],
        'region_name': ['Beijing', 'Shanghai', 'Guangzhou']
    }
    
    df = pd.DataFrame(data)
    df.to_csv('download_requests.csv', index=False)
    print("已创建示例CSV文件: download_requests.csv")
    
    # 从CSV文件读取并下载
    from prisma_downloader import create_download_requests_from_csv
    
    requests = create_download_requests_from_csv('download_requests.csv')
    
    downloader = PRISMADownloader(PRISMA_USERNAME, PRISMA_PASSWORD)
    
    try:
        results = downloader.batch_download(requests)
        print("CSV批量下载完成")
        
    finally:
        downloader.close()

def example_methane_monitoring():
    """甲烷监测专用下载示例"""
    print("=== 甲烷监测专用下载示例 ===")
    
    # 定义已知的甲烷排放源位置
    methane_sources = [
        {
            "name": "垃圾填埋场A",
            "lat": 40.1234,
            "lon": 116.5678,
            "buffer": 0.05  # 缓冲区大小（度）
        },
        {
            "name": "工业区B", 
            "lat": 39.8765,
            "lon": 116.4321,
            "buffer": 0.08
        }
    ]
    
    requests = []
    
    for source in methane_sources:
        # 为每个甲烷源创建下载请求
        request = DownloadRequest(
            start_date="2023-07-01",
            end_date="2023-07-31",
            min_lat=source["lat"] - source["buffer"],
            max_lat=source["lat"] + source["buffer"],
            min_lon=source["lon"] - source["buffer"],
            max_lon=source["lon"] + source["buffer"],
            cloud_cover=10,  # 甲烷检测需要低云覆盖
            product_level="L1"  # L1级别适合甲烷检测
        )
        requests.append(request)
    
    # 执行下载
    downloader = PRISMADownloader(PRISMA_USERNAME, PRISMA_PASSWORD)
    
    try:
        results = downloader.batch_download(requests)
        
        # 为每个源打印结果
        for source, (key, files) in zip(methane_sources, results.items()):
            print(f"{source['name']}: 下载了 {len(files)} 个文件")
            
    finally:
        downloader.close()

def create_download_template():
    """创建下载请求模板CSV文件"""
    print("=== 创建下载请求模板 ===")
    
    template_data = {
        'start_date': ['2023-01-01'],
        'end_date': ['2023-01-31'], 
        'min_lat': [39.0],
        'max_lat': [41.0],
        'min_lon': [116.0],
        'max_lon': [118.0],
        'cloud_cover': [20],
        'product_level': ['L1'],
        'region_name': ['Example_Region']
    }
    
    df = pd.DataFrame(template_data)
    df.to_csv('download_template.csv', index=False)
    
    print("已创建下载模板文件: download_template.csv")
    print("请编辑此文件，添加您的下载请求，然后使用 example_csv_batch_download() 函数")

if __name__ == "__main__":
    print("PRISMA批量下载器使用示例")
    print("请确保已在config.py中设置正确的用户名和密码")
    print()
    
    # 创建模板文件
    create_download_template()
    
    # 运行示例（请根据需要取消注释）
    # example_single_download()
    # example_multiple_regions()
    # example_time_series()
    # example_csv_batch_download()
    # example_methane_monitoring()
    
    print("\n所有示例代码已准备就绪！")
    print("请根据您的需求选择合适的示例运行。")
