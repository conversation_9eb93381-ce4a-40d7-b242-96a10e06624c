#!/usr/bin/env python3
"""
PRISMA数据快速下载脚本
简化版本，只需要提供基本参数即可快速下载数据
"""

from prisma_downloader import PRISMADownloader, DownloadRequest
import json

def quick_download(username, password, start_date, end_date, 
                  min_lat, max_lat, min_lon, max_lon, 
                  cloud_cover=20, product_level="L1"):
    """
    快速下载PRISMA数据
    
    参数:
        username: PRISMA用户名
        password: PRISMA密码
        start_date: 开始日期 "YYYY-MM-DD"
        end_date: 结束日期 "YYYY-MM-DD"
        min_lat: 最小纬度
        max_lat: 最大纬度
        min_lon: 最小经度
        max_lon: 最大经度
        cloud_cover: 云覆盖度阈值 (默认20%)
        product_level: 产品级别 (默认L1)
    
    返回:
        下载的文件列表
    """
    
    print("🛰️ PRISMA数据快速下载器")
    print("=" * 50)
    print(f"时间范围: {start_date} 到 {end_date}")
    print(f"地理范围: ({min_lat}, {min_lon}) 到 ({max_lat}, {max_lon})")
    print(f"云覆盖度: ≤{cloud_cover}%")
    print(f"产品级别: {product_level}")
    print("=" * 50)
    
    # 创建下载请求
    request = DownloadRequest(
        start_date=start_date,
        end_date=end_date,
        min_lat=min_lat,
        max_lat=max_lat,
        min_lon=min_lon,
        max_lon=max_lon,
        cloud_cover=cloud_cover,
        product_level=product_level
    )
    
    # 执行下载
    downloader = PRISMADownloader(username, password)
    
    try:
        print("🔐 正在登录...")
        results = downloader.batch_download([request])
        
        if results and list(results.values())[0]:
            files = list(results.values())[0]
            print(f"✅ 下载完成！共下载 {len(files)} 个文件:")
            for file in files:
                print(f"   📁 {file}")
            return files
        else:
            print("❌ 未找到符合条件的数据")
            return []
            
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return []
        
    finally:
        downloader.close()

def methane_detection_download(username, password, center_lat, center_lon, 
                             buffer=0.05, start_date="2023-07-01", end_date="2023-07-31"):
    """
    甲烷检测专用快速下载
    
    参数:
        username: PRISMA用户名
        password: PRISMA密码
        center_lat: 中心纬度
        center_lon: 中心经度
        buffer: 缓冲区大小（度，默认0.05度约5.5公里）
        start_date: 开始日期
        end_date: 结束日期
    """
    
    print("🔥 甲烷检测专用下载器")
    print("=" * 50)
    print(f"中心位置: ({center_lat}, {center_lon})")
    print(f"缓冲区: ±{buffer}度")
    print("优化参数: 低云覆盖度, L1级别产品")
    print("=" * 50)
    
    return quick_download(
        username=username,
        password=password,
        start_date=start_date,
        end_date=end_date,
        min_lat=center_lat - buffer,
        max_lat=center_lat + buffer,
        min_lon=center_lon - buffer,
        max_lon=center_lon + buffer,
        cloud_cover=10,  # 甲烷检测需要低云覆盖
        product_level="L1"  # L1级别适合光谱分析
    )

if __name__ == "__main__":
    # 使用示例
    print("请输入您的PRISMA账户信息:")
    username = input("用户名: ")
    password = input("密码: ")
    
    print("\n选择下载模式:")
    print("1. 自定义区域下载")
    print("2. 甲烷检测专用下载")
    
    choice = input("请选择 (1 或 2): ")
    
    if choice == "1":
        print("\n请输入下载参数:")
        start_date = input("开始日期 (YYYY-MM-DD): ")
        end_date = input("结束日期 (YYYY-MM-DD): ")
        min_lat = float(input("最小纬度: "))
        max_lat = float(input("最大纬度: "))
        min_lon = float(input("最小经度: "))
        max_lon = float(input("最大经度: "))
        
        cloud_cover = input("云覆盖度阈值 (默认20%): ")
        cloud_cover = int(cloud_cover) if cloud_cover else 20
        
        product_level = input("产品级别 (L0/L1/L2B/L2C/L2D, 默认L1): ")
        product_level = product_level if product_level else "L1"
        
        files = quick_download(
            username, password, start_date, end_date,
            min_lat, max_lat, min_lon, max_lon,
            cloud_cover, product_level
        )
        
    elif choice == "2":
        print("\n请输入甲烷源位置:")
        center_lat = float(input("中心纬度: "))
        center_lon = float(input("中心经度: "))
        
        buffer = input("缓冲区大小 (度, 默认0.05): ")
        buffer = float(buffer) if buffer else 0.05
        
        start_date = input("开始日期 (默认2023-07-01): ")
        start_date = start_date if start_date else "2023-07-01"
        
        end_date = input("结束日期 (默认2023-07-31): ")
        end_date = end_date if end_date else "2023-07-31"
        
        files = methane_detection_download(
            username, password, center_lat, center_lon,
            buffer, start_date, end_date
        )
    
    else:
        print("无效选择")
    
    print("\n🎉 下载任务完成！")
