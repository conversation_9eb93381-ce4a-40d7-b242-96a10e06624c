# PRISMA卫星数据批量下载器

一个类似Google Earth Engine的PRISMA卫星数据自动化批量下载工具，专为甲烷羽流检测研究设计。

## 🚀 功能特点

- **批量下载**: 支持多区域、多时间段的批量数据下载
- **自动化操作**: 模拟用户操作，自动登录和搜索数据
- **灵活配置**: 支持时间范围、地理边界、云覆盖度等参数设置
- **CSV批量处理**: 支持从CSV文件读取批量下载请求
- **甲烷检测优化**: 针对甲烷羽流检测需求优化的参数设置
- **进度监控**: 详细的日志记录和下载进度显示

## 📋 系统要求

- Python 3.8+
- Chrome浏览器
- PRISMA门户账户（需要注册）

## 🛠️ 安装步骤

### 1. 克隆或下载代码
```bash
git clone <repository_url>
cd prisma-downloader
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 安装Chrome驱动
```bash
# 方法1: 使用webdriver-manager（推荐）
pip install webdriver-manager

# 方法2: 手动下载ChromeDriver
# 访问 https://chromedriver.chromium.org/
# 下载对应版本的ChromeDriver并添加到PATH
```

### 4. 注册PRISMA账户
1. 访问 [PRISMA注册页面](https://prismauserregistration.asi.it)
2. 完成注册流程
3. 等待账户激活

### 5. 配置认证信息
编辑 `config.py` 文件，设置您的用户名和密码：
```python
PRISMA_USERNAME = 'your_username'
PRISMA_PASSWORD = 'your_password'
```

或者设置环境变量：
```bash
export PRISMA_USERNAME='your_username'
export PRISMA_PASSWORD='your_password'
```

## 🎯 快速开始

### 基本使用示例

```python
from prisma_downloader import PRISMADownloader, DownloadRequest

# 创建下载请求
request = DownloadRequest(
    start_date="2023-06-01",
    end_date="2023-06-30",
    min_lat=39.5,    # 最小纬度
    max_lat=40.5,    # 最大纬度
    min_lon=116.0,   # 最小经度
    max_lon=117.0,   # 最大经度
    cloud_cover=15,  # 云覆盖度阈值(%)
    product_level="L1"  # 产品级别
)

# 执行下载
downloader = PRISMADownloader("username", "password")
try:
    results = downloader.batch_download([request])
    print(f"下载完成: {results}")
finally:
    downloader.close()
```

### 甲烷检测专用示例

```python
# 针对甲烷检测优化的参数
methane_request = DownloadRequest(
    start_date="2023-07-01",
    end_date="2023-07-31",
    min_lat=40.0,
    max_lat=40.1,
    min_lon=116.4,
    max_lon=116.5,
    cloud_cover=10,     # 低云覆盖度
    product_level="L1"  # L1级别适合甲烷检测
)
```

## 📊 批量下载方式

### 1. 从CSV文件批量下载

创建CSV文件 `download_requests.csv`：
```csv
start_date,end_date,min_lat,max_lat,min_lon,max_lon,cloud_cover,product_level,region_name
2023-04-01,2023-04-30,39.0,41.0,116.0,118.0,15,L1,Beijing
2023-04-01,2023-04-30,31.0,32.0,121.0,122.0,20,L1,Shanghai
```

然后执行：
```python
from prisma_downloader import create_download_requests_from_csv

requests = create_download_requests_from_csv('download_requests.csv')
downloader = PRISMADownloader("username", "password")
results = downloader.batch_download(requests)
```

### 2. 多区域批量下载

```python
regions = [
    {"name": "北京", "bounds": (39.5, 40.5, 116.0, 117.0)},
    {"name": "上海", "bounds": (31.0, 31.5, 121.0, 121.8)},
    {"name": "广州", "bounds": (23.0, 23.5, 113.0, 113.8)}
]

requests = []
for region in regions:
    min_lat, max_lat, min_lon, max_lon = region["bounds"]
    request = DownloadRequest(
        start_date="2023-05-01",
        end_date="2023-05-31",
        min_lat=min_lat, max_lat=max_lat,
        min_lon=min_lon, max_lon=max_lon,
        cloud_cover=20, product_level="L1"
    )
    requests.append(request)
```

## 🔧 配置参数说明

### DownloadRequest参数
- `start_date`: 开始日期 (格式: "YYYY-MM-DD")
- `end_date`: 结束日期 (格式: "YYYY-MM-DD")
- `min_lat/max_lat`: 纬度范围
- `min_lon/max_lon`: 经度范围
- `cloud_cover`: 云覆盖度阈值 (0-100%)
- `product_level`: 产品级别 ("L0", "L1", "L2B", "L2C", "L2D")

### 产品级别说明
- **L0**: 原始数据流
- **L1**: 大气顶层辐射率数据（推荐用于甲烷检测）
- **L2B**: 地表反射率数据
- **L2C**: 大气校正数据
- **L2D**: 分析就绪数据

## 🔍 甲烷检测最佳实践

### 1. 参数设置建议
```python
# 甲烷检测优化参数
methane_params = {
    "cloud_cover": 10,      # 低云覆盖度
    "product_level": "L1",  # L1级别包含完整光谱信息
}
```

### 2. 时间选择策略
- 选择晴朗天气条件
- 避免雨季和多云时期
- 考虑太阳天顶角影响

### 3. 空间范围设置
- 包含已知甲烷源周边区域
- 考虑风向对羽流扩散的影响
- 设置适当的缓冲区

## 📝 运行示例

```bash
# 运行基本示例
python example_usage.py

# 创建下载模板
python -c "from example_usage import create_download_template; create_download_template()"
```

## 🐛 故障排除

### 常见问题

1. **登录失败**
   - 检查用户名和密码是否正确
   - 确认账户已激活
   - 检查网络连接

2. **Chrome驱动问题**
   ```bash
   pip install --upgrade webdriver-manager
   ```

3. **下载超时**
   - 增加 `DOWNLOAD_TIMEOUT` 值
   - 检查网络稳定性
   - 减少并发下载数量

4. **搜索结果为空**
   - 检查时间范围和地理边界
   - 调整云覆盖度阈值
   - 确认该区域有PRISMA数据覆盖

## 📊 数据后处理

下载完成后，您可以使用以下工具进行甲烷检测：

```python
# 使用PRISMA Toolbox处理数据
# 或者使用Python进行光谱分析
import rasterio
import numpy as np

# 读取PRISMA数据
with rasterio.open('prisma_file.he5') as src:
    data = src.read()
    
# 进行甲烷检测算法
# (具体算法实现见相关研究论文)
```

## 📚 相关资源

- [PRISMA官方文档](https://www.asi.it/en/earth-science/prisma/)
- [甲烷检测算法论文](https://amt.copernicus.org/articles/16/2627/2023/)
- [PRISMA数据处理工具](https://earthbit-support.planetek.it/)

## ⚠️ 注意事项

1. 请遵守PRISMA数据使用条款
2. 合理控制下载频率，避免对服务器造成压力
3. 定期备份下载的数据
4. 注意数据存储空间管理

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📄 许可证

MIT License
