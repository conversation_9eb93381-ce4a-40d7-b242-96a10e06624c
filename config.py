#!/usr/bin/env python3
"""
PRISMA下载器配置文件
"""

import os
from pathlib import Path

# PRISMA门户认证信息
PRISMA_USERNAME = os.getenv('PRISMA_USERNAME', 'your_username_here')
PRISMA_PASSWORD = os.getenv('PRISMA_PASSWORD', 'your_password_here')

# 下载配置
DOWNLOAD_DIR = Path('./prisma_data')
MAX_CONCURRENT_DOWNLOADS = 3
DOWNLOAD_TIMEOUT = 300  # 秒

# 浏览器配置
BROWSER_HEADLESS = True
BROWSER_TIMEOUT = 30

# 日志配置
LOG_LEVEL = 'INFO'
LOG_FILE = 'prisma_downloader.log'

# 重试配置
MAX_RETRY_ATTEMPTS = 3
RETRY_DELAY = 5  # 秒

# 产品级别映射
PRODUCT_LEVELS = {
    'L0': 'Level 0 - Raw data',
    'L1': 'Level 1 - TOA radiance',
    'L2B': 'Level 2B - Surface reflectance',
    'L2C': 'Level 2C - Atmospheric corrected',
    'L2D': 'Level 2D - Analysis ready data'
}

# 默认搜索参数
DEFAULT_CLOUD_COVER = 20
DEFAULT_PRODUCT_LEVEL = 'L1'

# 文件格式配置
SUPPORTED_FORMATS = ['.zip', '.tar.gz', '.hdf', '.nc']

# API端点（如果将来有API的话）
PRISMA_API_BASE_URL = 'https://prisma.asi.it/api/v1'
