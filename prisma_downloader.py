#!/usr/bin/env python3
"""
PRISMA Satellite Data Batch Downloader
类似GEE的批量下载工具，用于自动化下载PRISMA卫星数据

作者: AI Assistant
日期: 2025-01-03
"""

import os
import time
import json
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import requests
from pathlib import Path

@dataclass
class DownloadRequest:
    """下载请求数据结构"""
    start_date: str  # 格式: "YYYY-MM-DD"
    end_date: str    # 格式: "YYYY-MM-DD"
    min_lat: float   # 最小纬度
    max_lat: float   # 最大纬度
    min_lon: float   # 最小经度
    max_lon: float   # 最大经度
    cloud_cover: int = 20  # 云覆盖度阈值 (%)
    product_level: str = "L1"  # 产品级别: L0, L1, L2B, L2C, L2D

class PRISMADownloader:
    """PRISMA卫星数据自动化下载器"""
    
    def __init__(self, username: str, password: str, download_dir: str = "./prisma_data"):
        """
        初始化下载器
        
        Args:
            username: PRISMA门户用户名
            password: PRISMA门户密码
            download_dir: 数据下载目录
        """
        self.username = username
        self.password = password
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('prisma_downloader.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 初始化浏览器
        self.driver = None
        self._setup_driver()
        
    def _setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # 无头模式
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        # 设置下载目录
        prefs = {
            "download.default_directory": str(self.download_dir.absolute()),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 30)
            self.logger.info("Chrome驱动初始化成功")
        except Exception as e:
            self.logger.error(f"Chrome驱动初始化失败: {e}")
            raise
    
    def login(self) -> bool:
        """登录PRISMA门户"""
        try:
            self.logger.info("正在登录PRISMA门户...")
            self.driver.get("http://prisma.asi.it/missionselect/")
            
            # 等待登录页面加载
            username_field = self.wait.until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            password_field = self.driver.find_element(By.NAME, "password")
            
            # 输入凭据
            username_field.clear()
            username_field.send_keys(self.username)
            password_field.clear()
            password_field.send_keys(self.password)
            
            # 点击登录按钮
            login_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Sign In')]")
            login_button.click()
            
            # 等待登录成功
            self.wait.until(EC.url_contains("missionselect"))
            self.logger.info("登录成功")
            return True
            
        except TimeoutException:
            self.logger.error("登录超时")
            return False
        except Exception as e:
            self.logger.error(f"登录失败: {e}")
            return False
    
    def search_data(self, request: DownloadRequest) -> List[Dict]:
        """
        搜索符合条件的PRISMA数据
        
        Args:
            request: 下载请求参数
            
        Returns:
            符合条件的数据产品列表
        """
        try:
            self.logger.info(f"搜索数据: {request.start_date} 到 {request.end_date}")
            
            # 导航到搜索页面
            search_url = "http://prisma.asi.it/missionselect/search"
            self.driver.get(search_url)
            
            # 设置时间范围
            start_date_field = self.wait.until(
                EC.presence_of_element_located((By.ID, "startDate"))
            )
            end_date_field = self.driver.find_element(By.ID, "endDate")
            
            start_date_field.clear()
            start_date_field.send_keys(request.start_date)
            end_date_field.clear()
            end_date_field.send_keys(request.end_date)
            
            # 设置地理范围
            self._set_geographic_bounds(request)
            
            # 设置产品级别
            self._set_product_level(request.product_level)
            
            # 设置云覆盖度
            self._set_cloud_cover(request.cloud_cover)
            
            # 执行搜索
            search_button = self.driver.find_element(By.ID, "searchButton")
            search_button.click()
            
            # 等待搜索结果
            self.wait.until(EC.presence_of_element_located((By.CLASS_NAME, "search-results")))
            
            # 解析搜索结果
            results = self._parse_search_results()
            self.logger.info(f"找到 {len(results)} 个数据产品")
            
            return results
            
        except Exception as e:
            self.logger.error(f"搜索数据失败: {e}")
            return []
    
    def _set_geographic_bounds(self, request: DownloadRequest):
        """设置地理边界"""
        try:
            # 设置经纬度范围
            min_lat_field = self.driver.find_element(By.ID, "minLat")
            max_lat_field = self.driver.find_element(By.ID, "maxLat")
            min_lon_field = self.driver.find_element(By.ID, "minLon")
            max_lon_field = self.driver.find_element(By.ID, "maxLon")
            
            min_lat_field.clear()
            min_lat_field.send_keys(str(request.min_lat))
            max_lat_field.clear()
            max_lat_field.send_keys(str(request.max_lat))
            min_lon_field.clear()
            min_lon_field.send_keys(str(request.min_lon))
            max_lon_field.clear()
            max_lon_field.send_keys(str(request.max_lon))
            
        except NoSuchElementException:
            self.logger.warning("无法设置地理边界，使用默认设置")
    
    def _set_product_level(self, level: str):
        """设置产品级别"""
        try:
            level_dropdown = self.driver.find_element(By.ID, "productLevel")
            level_dropdown.click()
            
            level_option = self.driver.find_element(By.XPATH, f"//option[@value='{level}']")
            level_option.click()
            
        except NoSuchElementException:
            self.logger.warning(f"无法设置产品级别为 {level}")
    
    def _set_cloud_cover(self, cloud_cover: int):
        """设置云覆盖度阈值"""
        try:
            cloud_field = self.driver.find_element(By.ID, "cloudCover")
            cloud_field.clear()
            cloud_field.send_keys(str(cloud_cover))
            
        except NoSuchElementException:
            self.logger.warning("无法设置云覆盖度")
    
    def _parse_search_results(self) -> List[Dict]:
        """解析搜索结果"""
        results = []
        try:
            # 等待结果表格加载
            time.sleep(3)
            result_rows = self.driver.find_elements(By.CSS_SELECTOR, ".search-results tbody tr")
            
            for row in result_rows:
                cells = row.find_elements(By.TAG_NAME, "td")
                if len(cells) >= 6:
                    result = {
                        'product_id': cells[0].text.strip(),
                        'acquisition_date': cells[1].text.strip(),
                        'product_level': cells[2].text.strip(),
                        'cloud_cover': cells[3].text.strip(),
                        'size_mb': cells[4].text.strip(),
                        'download_link': cells[5].find_element(By.TAG_NAME, "a").get_attribute("href")
                    }
                    results.append(result)
                    
        except Exception as e:
            self.logger.error(f"解析搜索结果失败: {e}")
            
        return results
    
    def download_products(self, products: List[Dict]) -> List[str]:
        """
        批量下载数据产品
        
        Args:
            products: 要下载的产品列表
            
        Returns:
            成功下载的文件路径列表
        """
        downloaded_files = []
        
        for i, product in enumerate(products, 1):
            self.logger.info(f"下载进度: {i}/{len(products)} - {product['product_id']}")
            
            try:
                # 点击下载链接
                self.driver.get(product['download_link'])
                
                # 等待下载开始
                time.sleep(5)
                
                # 检查下载是否完成
                file_path = self._wait_for_download(product['product_id'])
                if file_path:
                    downloaded_files.append(file_path)
                    self.logger.info(f"下载完成: {file_path}")
                else:
                    self.logger.error(f"下载失败: {product['product_id']}")
                    
            except Exception as e:
                self.logger.error(f"下载产品 {product['product_id']} 失败: {e}")
                
        return downloaded_files
    
    def _wait_for_download(self, product_id: str, timeout: int = 300) -> Optional[str]:
        """等待文件下载完成"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # 检查下载目录中的文件
            for file_path in self.download_dir.glob("*"):
                if product_id in file_path.name and not file_path.name.endswith('.crdownload'):
                    return str(file_path)
            
            time.sleep(2)
            
        return None
    
    def batch_download(self, requests: List[DownloadRequest]) -> Dict[str, List[str]]:
        """
        批量下载多个请求的数据
        
        Args:
            requests: 下载请求列表
            
        Returns:
            下载结果字典
        """
        results = {}
        
        if not self.login():
            self.logger.error("登录失败，无法继续下载")
            return results
        
        for i, request in enumerate(requests, 1):
            self.logger.info(f"处理请求 {i}/{len(requests)}")
            
            # 搜索数据
            products = self.search_data(request)
            
            if products:
                # 下载数据
                downloaded_files = self.download_products(products)
                results[f"request_{i}"] = downloaded_files
            else:
                self.logger.warning(f"请求 {i} 未找到符合条件的数据")
                results[f"request_{i}"] = []
                
        return results
    
    def close(self):
        """关闭浏览器驱动"""
        if self.driver:
            self.driver.quit()
            self.logger.info("浏览器驱动已关闭")

def create_download_requests_from_csv(csv_file: str) -> List[DownloadRequest]:
    """从CSV文件创建下载请求列表"""
    df = pd.read_csv(csv_file)
    requests = []
    
    for _, row in df.iterrows():
        request = DownloadRequest(
            start_date=row['start_date'],
            end_date=row['end_date'],
            min_lat=row['min_lat'],
            max_lat=row['max_lat'],
            min_lon=row['min_lon'],
            max_lon=row['max_lon'],
            cloud_cover=row.get('cloud_cover', 20),
            product_level=row.get('product_level', 'L1')
        )
        requests.append(request)
        
    return requests

# 使用示例
if __name__ == "__main__":
    # 配置参数
    USERNAME = "your_username"  # 替换为您的用户名
    PASSWORD = "your_password"  # 替换为您的密码
    
    # 创建下载请求
    download_requests = [
        DownloadRequest(
            start_date="2023-01-01",
            end_date="2023-01-31",
            min_lat=39.0,
            max_lat=41.0,
            min_lon=116.0,
            max_lon=118.0,
            cloud_cover=10,
            product_level="L1"
        )
    ]
    
    # 执行下载
    downloader = PRISMADownloader(USERNAME, PASSWORD)
    
    try:
        results = downloader.batch_download(download_requests)
        print("下载完成！")
        print(json.dumps(results, indent=2, ensure_ascii=False))
        
    finally:
        downloader.close()
